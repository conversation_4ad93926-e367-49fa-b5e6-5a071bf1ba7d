version: '3.8'

services:
  # API service
  api:
    image: langgenius/dify-api:0.6.16
    restart: always
    environment:
      # Startup mode, 'api' starts the API server.
      MODE: api
      # The log level for the application. Supported values are `DEBUG`, `INFO`, `WARNING`, `ERROR`, `CRITICAL`
      LOG_LEVEL: INFO
      # A secret key that is used for securely signing the session cookie and encrypting sensitive information on the database. You can generate a strong key using `openssl rand -base64 42`.
      SECRET_KEY: ************************************************
      # The base URL of console application web frontend, refers to the Console base URL of WEB service if console domain is
      # different from api or web app domain.
      # example: http://cloud.dify.ai/console
      CONSOLE_WEB_URL: ''
      # Password for admin user initialization.
      # If left unset, admin user will not be prompted for a password when first logging in.
      INIT_PASSWORD: ''
      # The base URL of console application api server, refers to the Console base URL of WEB service if console domain is
      # different from api or web app domain.
      # example: http://cloud.dify.ai/console/api
      CONSOLE_API_URL: ''
      # The URL prefix for Service API endpoints, refers to the base URL of the current API service if api domain is
      # different from console domain.
      # example: http://cloud.dify.ai/api
      SERVICE_API_URL: ''
      # The URL prefix for Web APP frontend, refers to the Web App base URL of WEB service if web app domain is different from
      # console or api domain.
      # example: http://udify.app/chat
      APP_WEB_URL: ''
      # File preview or download URL prefix.
      # used to display File preview or download URL to the front-end or as Multi-model inputs;
      # Url is signed and has expiration time.
      FILES_URL: ''
      # When enabled, migrations will be executed prior to application startup and the application will start after the migrations are completed.
      MIGRATION_ENABLED: 'true'
      # The configurations of postgres database connection.
      # It is consistent with the configuration in the 'db' service below.
      DB_USERNAME: postgres
      DB_PASSWORD: difyai123456
      DB_HOST: db
      DB_PORT: 5432
      DB_DATABASE: dify
      # The configurations of redis connection.
      # It is consistent with the configuration in the 'redis' service below.
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_USERNAME: ''
      REDIS_PASSWORD: difyai123456
      REDIS_DB: 0
      # use redis db 1 for celery broker.
      CELERY_BROKER_URL: redis://:difyai123456@redis:6379/1
      # The configurations of celery worker.
      WORKER_TIMEOUT: 200
      # The configurations of storage and upload.
      STORAGE_TYPE: local
      STORAGE_LOCAL_PATH: storage
      # The Vector store configurations.
      VECTOR_STORE: qdrant
      QDRANT_URL: http://qdrant:6333
      QDRANT_API_KEY: ''
      # Mail configuration, support: resend, smtp
      MAIL_TYPE: ''
      # default send from email address, if not specified
      MAIL_DEFAULT_SEND_FROM: ''
      RESEND_API_KEY: ''
      RESEND_API_URL: https://api.resend.com
      # smtp configuration
      SMTP_SERVER: ''
      SMTP_PORT: 587
      SMTP_USERNAME: ''
      SMTP_PASSWORD: ''
      SMTP_USE_TLS: 'true'
      # WeChat Official Account Configurations
      WECHAT_APP_ID: ''
      WECHAT_APP_SECRET: ''
      WECHAT_TOKEN: ''
      WECHAT_AES_KEY: ''
    depends_on:
      - db
      - redis
    volumes:
      # Mount the storage directory to the container, for storing user files.
      - ./volumes/app/storage:/app/api/storage
    networks:
      - dify

  # worker service
  # The Celery worker for processing the queue.
  worker:
    image: langgenius/dify-api:0.6.16
    restart: always
    environment:
      # Startup mode, 'worker' starts the Celery worker for processing the queue.
      MODE: worker
      # --- All the configurations below are the same as those in the 'api' service. ---
      LOG_LEVEL: INFO
      SECRET_KEY: ************************************************
      DB_USERNAME: postgres
      DB_PASSWORD: difyai123456
      DB_HOST: db
      DB_PORT: 5432
      DB_DATABASE: dify
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_USERNAME: ''
      REDIS_PASSWORD: difyai123456
      REDIS_DB: 0
      CELERY_BROKER_URL: redis://:difyai123456@redis:6379/1
      WORKER_TIMEOUT: 200
      STORAGE_TYPE: local
      STORAGE_LOCAL_PATH: storage
      VECTOR_STORE: qdrant
      QDRANT_URL: http://qdrant:6333
      QDRANT_API_KEY: ''
    depends_on:
      - db
      - redis
    volumes:
      # Mount the storage directory to the container, for storing user files.
      - ./volumes/app/storage:/app/api/storage
    networks:
      - dify

  # Frontend web application.
  web:
    image: langgenius/dify-web:0.6.16
    restart: always
    environment:
      # The base URL of console application api server, refers to the Console base URL of WEB service if console domain is
      # different from api or web app domain.
      # example: http://cloud.dify.ai/console/api
      CONSOLE_API_URL: ''
      # The URL prefix for Web APP frontend, refers to the Web App base URL of WEB service if web app domain is different from
      # console or api domain.
      # example: http://udify.app/chat
      APP_API_URL: ''
    depends_on:
      - api
    networks:
      - dify

  # The postgres database.
  db:
    image: postgres:15-alpine
    restart: always
    environment:
      PGUSER: postgres
      POSTGRES_PASSWORD: difyai123456
      POSTGRES_DB: dify
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - ./volumes/db/data:/var/lib/postgresql/data
    networks:
      - dify
    healthcheck:
      test: ['CMD', 'pg_isready']
      interval: 1s
      timeout: 3s
      retries: 30

  # The redis cache.
  redis:
    image: redis:6-alpine
    restart: always
    command: redis-server --requirepass difyai123456
    volumes:
      - ./volumes/redis/data:/data
    networks:
      - dify
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']

  # The DifySandbox
  sandbox:
    image: langgenius/dify-sandbox:0.2.1
    restart: always
    environment:
      # The DifySandbox configurations
      # Make sure you are changing this key for your deployment with a strong key.
      # You can generate a strong key using `openssl rand -base64 42`.
      API_KEY: dify-sandbox
      GIN_MODE: 'release'
      WORKER_TIMEOUT: 15
    volumes:
      - ./volumes/sandbox/dependencies:/dependencies
    networks:
      - dify
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:8194/health']

  # Qdrant vector database.
  qdrant:
    image: langgenius/qdrant:v1.7.3
    restart: always
    volumes:
      - ./volumes/qdrant:/qdrant/storage
    environment:
      QDRANT__SERVICE__HTTP_PORT: 6333
      QDRANT__SERVICE__GRPC_PORT: 6334
    networks:
      - dify

  # The nginx reverse proxy.
  # used for reverse proxying the API service and Web service.
  nginx:
    image: nginx:latest
    restart: always
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - api
      - web
    ports:
      - "80:80"
    networks:
      - dify

networks:
  dify:
    driver: bridge
