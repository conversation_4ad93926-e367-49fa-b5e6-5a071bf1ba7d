"""
Browser Use AI Agent 基础使用示例
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from browser_agent.agent import AIAgent


async def example_navigation():
    """示例：网页导航"""
    agent = AIAgent()
    await agent.initialize()
    
    try:
        print("=== 网页导航示例 ===")
        
        # 导航到百度
        result = await agent.execute_browser_task(
            "navigate", 
            url="https://www.baidu.com"
        )
        print(f"导航结果: {result}")
        
        # 获取页面信息
        page_info = await agent.get_page_status()
        print(f"页面标题: {page_info.get('title')}")
        print(f"页面URL: {page_info.get('url')}")
        
    finally:
        await agent.close()


async def example_form_filling():
    """示例：表单填充"""
    agent = AIAgent()
    await agent.initialize()
    
    try:
        print("=== 表单填充示例 ===")
        
        # 先导航到有表单的页面
        await agent.execute_browser_task(
            "navigate", 
            url="https://www.baidu.com"
        )
        
        # 填充搜索表单
        form_data = {
            "搜索框": "Python编程",
        }
        
        result = await agent.execute_browser_task(
            "fill_form", 
            form_data=form_data,
            submit=True
        )
        print(f"表单填充结果: {result}")
        
    finally:
        await agent.close()


async def example_data_extraction():
    """示例：数据提取"""
    agent = AIAgent()
    await agent.initialize()
    
    try:
        print("=== 数据提取示例 ===")
        
        # 导航到新闻网站
        await agent.execute_browser_task(
            "navigate", 
            url="https://news.baidu.com"
        )
        
        # 提取数据
        extraction_rules = [
            "提取页面中的新闻标题",
            "提取新闻链接",
            "提取发布时间"
        ]
        
        result = await agent.execute_browser_task(
            "extract_data", 
            extraction_rules=extraction_rules
        )
        print(f"数据提取结果: {result}")
        
    finally:
        await agent.close()


async def example_chat_interaction():
    """示例：对话交互"""
    agent = AIAgent()
    await agent.initialize()
    
    try:
        print("=== 对话交互示例 ===")
        
        # 模拟用户对话
        user_inputs = [
            "你好，请帮我打开百度网站",
            "在搜索框中输入'人工智能'",
            "点击搜索按钮",
            "截图保存当前页面"
        ]
        
        for user_input in user_inputs:
            print(f"\n用户: {user_input}")
            
            response = await agent.chat(user_input)
            print(f"AI: {response['response']}")
            
            # 如果有浏览器操作，执行它
            if response.get("browser_action"):
                action = response["browser_action"]
                print(f"执行操作: {action['description']}")
                
                if action["type"] == "navigate":
                    result = await agent.execute_browser_task(
                        "navigate", url=action["url"]
                    )
                elif action["type"] == "screenshot":
                    result = await agent.execute_browser_task("screenshot")
                
                print(f"操作结果: {result}")
        
    finally:
        await agent.close()


async def main():
    """主函数"""
    print("Browser Use AI Agent 示例程序")
    print("=" * 40)
    
    examples = {
        "1": ("网页导航", example_navigation),
        "2": ("表单填充", example_form_filling),
        "3": ("数据提取", example_data_extraction),
        "4": ("对话交互", example_chat_interaction)
    }
    
    print("请选择要运行的示例:")
    for key, (name, _) in examples.items():
        print(f"{key}. {name}")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    if choice in examples:
        name, func = examples[choice]
        print(f"\n运行示例: {name}")
        await func()
    else:
        print("无效选择")


if __name__ == "__main__":
    asyncio.run(main())
