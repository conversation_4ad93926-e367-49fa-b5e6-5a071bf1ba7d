"""
测试持久化浏览器 - 确保浏览器保持打开状态
"""
import asyncio
import os
from browser_use import Agent
from langchain_openai import ChatOpenAI

async def test_persistent_browser():
    """测试持久化浏览器"""
    
    # 设置环境变量
    os.environ["OPENAI_API_KEY"] = "***********************************"
    os.environ["OPENAI_API_BASE"] = "https://api.deepseek.com/v1"
    os.environ["OPENAI_BASE_URL"] = "https://api.deepseek.com/v1"
    
    # 强制设置浏览器为可见模式
    os.environ["BROWSER_USE_HEADLESS"] = "false"
    os.environ["PLAYWRIGHT_HEADLESS"] = "false"
    os.environ["HEADLESS"] = "false"
    os.environ["BROWSER_HEADLESS"] = "false"
    os.environ["PWDEBUG"] = "1"
    
    print("🚀 启动持久化浏览器测试...")
    
    # 创建LLM客户端
    llm = ChatOpenAI(
        model="deepseek-chat",
        api_key="***********************************",
        base_url="https://api.deepseek.com/v1",
        temperature=0.7,
        max_tokens=500,
        timeout=30
    )
    
    # 创建Agent
    agent = Agent(
        task="打开百度网站并保持浏览器窗口打开",
        llm=llm
    )
    
    print("🌐 正在启动浏览器...")
    print("📌 请注意观察浏览器窗口")
    
    try:
        # 第一个任务：打开百度
        print("\n🔧 任务1：打开百度网站")
        result1 = await agent.run(max_steps=3)
        print(f"✅ 任务1完成")
        
        # 等待用户确认
        input("\n👀 您看到浏览器窗口了吗？按回车继续...")
        
        # 第二个任务：搜索
        print("\n🔧 任务2：搜索'人工智能'")
        agent.task = "在百度搜索框中输入'人工智能'并点击搜索"
        result2 = await agent.run(max_steps=5)
        print(f"✅ 任务2完成")
        
        # 等待用户确认
        input("\n👀 您看到搜索结果了吗？按回车继续...")
        
        # 第三个任务：截图
        print("\n🔧 任务3：截图保存")
        agent.task = "截取当前页面的截图"
        result3 = await agent.run(max_steps=2)
        print(f"✅ 任务3完成")
        
        print("\n🎉 所有任务完成！")
        print("📝 如果您看到了浏览器窗口和操作过程，说明配置正确")
        
        # 最终等待
        input("\n按回车键结束测试...")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_persistent_browser())
