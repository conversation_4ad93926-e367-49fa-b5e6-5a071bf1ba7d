# Dify 启动脚本
Write-Host "正在启动 Dify AI 平台..." -ForegroundColor Green

# 检查 Docker 是否运行
try {
    docker version | Out-Null
    Write-Host "✅ Docker 运行正常" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker 未运行，请先启动 Docker Desktop" -ForegroundColor Red
    exit 1
}

# 检查 Docker Compose 是否可用
try {
    docker-compose version | Out-Null
    Write-Host "✅ Docker Compose 可用" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose 不可用" -ForegroundColor Red
    exit 1
}

# 创建必要的目录
Write-Host "创建数据目录..." -ForegroundColor Yellow
New-Item -ItemType Directory -Path "volumes/app/storage", "volumes/db/data", "volumes/redis/data", "volumes/qdrant", "volumes/sandbox/dependencies" -Force | Out-Null

# 启动服务
Write-Host "启动 Dify 服务..." -ForegroundColor Yellow
docker-compose up -d

# 等待服务启动
Write-Host "等待服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# 检查服务状态
Write-Host "检查服务状态..." -ForegroundColor Yellow
docker-compose ps

Write-Host ""
Write-Host "🎉 Dify 启动完成!" -ForegroundColor Green
Write-Host "访问地址: http://localhost" -ForegroundColor Cyan
Write-Host "管理员密码: admin123456" -ForegroundColor Cyan
Write-Host ""
Write-Host "请在浏览器中访问 http://localhost 开始使用 Dify" -ForegroundColor Yellow
