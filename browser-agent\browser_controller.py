"""
浏览器控制器 - 基于Browser Use
"""
import asyncio
import logging
import os
from typing import Optional, Dict, Any, List
from browser_use import Agent
from langchain_openai import ChatOpenAI
from config import get_browser_config, get_deepseek_config

logger = logging.getLogger(__name__)


class BrowserController:
    """浏览器控制器"""

    def __init__(self):
        self.agent: Optional[Agent] = None
        self.browser_config = get_browser_config()
        self.deepseek_config = get_deepseek_config()

    async def initialize(self):
        """初始化浏览器和AI代理"""
        try:
            # 设置环境变量以兼容OpenAI API
            os.environ["OPENAI_API_KEY"] = self.deepseek_config["api_key"]
            os.environ["OPENAI_API_BASE"] = self.deepseek_config["base_url"]

            # 创建LLM客户端
            llm = self._create_llm_client()

            # 创建AI代理
            self.agent = Agent(
                task="Browser automation assistant",
                llm=llm
            )

            logger.info("浏览器控制器初始化成功")

        except Exception as e:
            logger.error(f"浏览器控制器初始化失败: {e}")
            raise

    def _create_llm_client(self):
        """创建LLM客户端"""
        return ChatOpenAI(
            model=self.deepseek_config["model"],
            api_key=self.deepseek_config["api_key"],
            base_url=self.deepseek_config["base_url"],
            temperature=0.7
        )

    async def execute_task(self, task: str, url: Optional[str] = None) -> Dict[str, Any]:
        """执行浏览器任务"""
        if not self.agent:
            raise RuntimeError("浏览器控制器未初始化")

        try:
            # 更新任务描述
            self.agent.task = task

            # 执行任务
            result = await self.agent.run(url)

            return {
                "success": True,
                "result": str(result),
                "message": "任务执行成功"
            }

        except Exception as e:
            logger.error(f"任务执行失败: {e}")
            return {
                "success": False,
                "result": None,
                "message": f"任务执行失败: {str(e)}"
            }

    async def fill_form(self, form_data: Dict[str, str], submit: bool = False) -> Dict[str, Any]:
        """智能表单填充"""
        task_parts = ["请帮我填充以下表单:"]

        for field, value in form_data.items():
            task_parts.append(f"- {field}: {value}")

        if submit:
            task_parts.append("填充完成后提交表单")

        task = "\n".join(task_parts)
        return await self.execute_task(task)

    async def extract_data(self, extraction_rules: List[str]) -> Dict[str, Any]:
        """数据提取"""
        task_parts = ["请帮我从当前页面提取以下数据:"]
        task_parts.extend([f"- {rule}" for rule in extraction_rules])

        task = "\n".join(task_parts)
        return await self.execute_task(task)

    async def navigate_and_interact(self, url: str, actions: List[str]) -> Dict[str, Any]:
        """导航并执行交互操作"""
        task_parts = [f"请访问 {url} 并执行以下操作:"]
        task_parts.extend([f"- {action}" for action in actions])

        task = "\n".join(task_parts)
        return await self.execute_task(task, url)

    async def take_screenshot(self) -> Optional[str]:
        """截取当前页面截图"""
        try:
            result = await self.execute_task("请截取当前页面的截图")
            if result["success"]:
                return "screenshot.png"
        except Exception as e:
            logger.error(f"截图失败: {e}")
        return None

    async def get_page_info(self) -> Dict[str, Any]:
        """获取当前页面信息"""
        try:
            result = await self.execute_task("请获取当前页面的URL、标题和基本信息")
            if result["success"]:
                return {"info": result["result"]}
        except Exception as e:
            logger.error(f"获取页面信息失败: {e}")
        return {}

    async def close(self):
        """关闭浏览器"""
        try:
            if self.agent:
                # Browser Use会自动管理浏览器生命周期
                logger.info("浏览器会话已结束")
        except Exception as e:
            logger.error(f"关闭浏览器失败: {e}")

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
