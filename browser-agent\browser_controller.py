"""
浏览器控制器 - 基于Browser Use
"""
import asyncio
import logging
from typing import Optional, Dict, Any, List
from browser_use import Agent
from browser_use.browser.browser import Browser
from browser_use.browser.context import BrowserContext
from config import get_browser_config, get_deepseek_config

logger = logging.getLogger(__name__)


class BrowserController:
    """浏览器控制器"""

    def __init__(self):
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.agent: Optional[Agent] = None
        self.browser_config = get_browser_config()
        self.deepseek_config = get_deepseek_config()

    async def initialize(self):
        """初始化浏览器和AI代理"""
        try:
            # 创建浏览器实例
            self.browser = Browser(
                browser_type=self.browser_config["browser_type"],
                headless=self.browser_config["headless"]
            )

            # 启动浏览器
            await self.browser.start()

            # 创建浏览器上下文
            self.context = await self.browser.new_context()

            # 创建AI代理
            self.agent = Agent(
                task="",  # 任务将在执行时设置
                llm=self._create_llm_client(),
                browser_context=self.context
            )

            logger.info("浏览器控制器初始化成功")

        except Exception as e:
            logger.error(f"浏览器控制器初始化失败: {e}")
            raise

    def _create_llm_client(self):
        """创建LLM客户端"""
        from openai import OpenAI

        return OpenAI(
            api_key=self.deepseek_config["api_key"],
            base_url=self.deepseek_config["base_url"]
        )

    async def execute_task(self, task: str, url: Optional[str] = None) -> Dict[str, Any]:
        """执行浏览器任务"""
        if not self.agent:
            raise RuntimeError("浏览器控制器未初始化")

        try:
            # 设置任务
            self.agent.task = task

            # 如果提供了URL，先导航到该页面
            if url:
                page = await self.context.new_page()
                await page.goto(url)
                logger.info(f"导航到页面: {url}")

            # 执行任务
            result = await self.agent.run()

            return {
                "success": True,
                "result": result,
                "message": "任务执行成功"
            }

        except Exception as e:
            logger.error(f"任务执行失败: {e}")
            return {
                "success": False,
                "result": None,
                "message": f"任务执行失败: {str(e)}"
            }

    async def fill_form(self, form_data: Dict[str, str], submit: bool = False) -> Dict[str, Any]:
        """智能表单填充"""
        task_parts = ["请帮我填充以下表单:"]

        for field, value in form_data.items():
            task_parts.append(f"- {field}: {value}")

        if submit:
            task_parts.append("填充完成后提交表单")

        task = "\n".join(task_parts)
        return await self.execute_task(task)

    async def extract_data(self, extraction_rules: List[str]) -> Dict[str, Any]:
        """数据提取"""
        task_parts = ["请帮我从当前页面提取以下数据:"]
        task_parts.extend([f"- {rule}" for rule in extraction_rules])

        task = "\n".join(task_parts)
        return await self.execute_task(task)

    async def navigate_and_interact(self, url: str, actions: List[str]) -> Dict[str, Any]:
        """导航并执行交互操作"""
        task_parts = [f"请访问 {url} 并执行以下操作:"]
        task_parts.extend([f"- {action}" for action in actions])

        task = "\n".join(task_parts)
        return await self.execute_task(task, url)

    async def take_screenshot(self) -> Optional[str]:
        """截取当前页面截图"""
        try:
            if self.context:
                pages = self.context.pages
                if pages:
                    screenshot_path = "screenshot.png"
                    await pages[0].screenshot(path=screenshot_path)
                    return screenshot_path
        except Exception as e:
            logger.error(f"截图失败: {e}")
        return None

    async def get_page_info(self) -> Dict[str, Any]:
        """获取当前页面信息"""
        try:
            if self.context and self.context.pages:
                page = self.context.pages[0]
                return {
                    "url": page.url,
                    "title": await page.title(),
                    "content": await page.content()
                }
        except Exception as e:
            logger.error(f"获取页面信息失败: {e}")
        return {}

    async def close(self):
        """关闭浏览器"""
        try:
            if self.browser:
                await self.browser.close()
                logger.info("浏览器已关闭")
        except Exception as e:
            logger.error(f"关闭浏览器失败: {e}")

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
