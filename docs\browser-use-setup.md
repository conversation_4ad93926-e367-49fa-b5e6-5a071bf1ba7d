# Browser Use AI Agent 部署指南

## 🎯 项目概述

Browser Use AI Agent 是一个基于Browser Use的轻量级AI智能体，专注于浏览器自动化操作。通过自然语言对话，用户可以让AI帮助执行各种浏览器任务。

## 📋 前置要求

1. **Python 3.8+** - 确保已安装Python
   - 下载地址: https://www.python.org/downloads/
   - 验证安装: `python --version`

2. **DeepSeek API Key** - 从DeepSeek官网获取
   - 注册地址: https://platform.deepseek.com/
   - 获取API Key并记录

3. **Chrome/Chromium浏览器** - Playwright需要
   - 安装脚本会自动下载浏览器

## 🚀 快速安装

### 1. 克隆或下载项目
确保您在项目根目录 `AI_Agent/`

### 2. 运行安装脚本
```powershell
.\install.ps1
```

安装脚本会自动：
- 检查Python环境
- 创建虚拟环境
- 安装所有依赖包
- 下载Playwright浏览器

### 3. 配置API Key
编辑 `.env` 文件：
```bash
DEEPSEEK_API_KEY=sk-your-actual-api-key-here
```

### 4. 启动程序
```powershell
.\start.ps1
```

## 🔧 手动安装（可选）

如果自动安装失败，可以手动执行：

```powershell
# 1. 创建虚拟环境
python -m venv venv

# 2. 激活虚拟环境
venv\Scripts\Activate.ps1

# 3. 安装依赖
pip install -r requirements.txt

# 4. 安装浏览器
playwright install

# 5. 配置环境变量
# 编辑 .env 文件

# 6. 启动程序
python browser-agent/main.py
```

## 💡 使用指南

### 基本命令
- `help` - 显示帮助信息
- `status` - 显示当前页面状态
- `screenshot` - 截取当前页面
- `quit` - 退出程序

### 对话示例
```
👤 您: 打开百度网站
🤖 AI: 好的，我来帮您打开百度网站
🔧 执行操作: 导航到指定网页
✅ 操作成功: 完成

👤 您: 在搜索框中输入"人工智能"
🤖 AI: 我来帮您在搜索框中输入"人工智能"

👤 您: 提取页面中的所有链接
🤖 AI: 我来帮您提取页面中的链接信息
```

### 支持的操作类型
1. **网页导航**
   - "打开 https://www.example.com"
   - "访问百度网站"

2. **表单操作**
   - "填写表单：用户名是张三，密码是123456"
   - "在搜索框中输入关键词"

3. **数据提取**
   - "提取页面中的所有链接"
   - "获取新闻标题和时间"

4. **页面交互**
   - "点击搜索按钮"
   - "选择下拉菜单中的选项"

5. **截图保存**
   - "截图保存当前页面"
   - "screenshot"

## 🔍 功能特性

### ✅ 已实现功能
- 🤖 自然语言对话界面
- 🌐 网页导航和浏览
- 📝 智能表单填充
- 📊 数据提取和收集
- 📸 页面截图功能
- 🔄 多轮对话记忆

### 🚧 计划功能
- 📚 本地知识库集成
- 🔗 与Dify平台集成
- 📱 Web界面
- 🔧 更多浏览器操作
- 📈 任务执行历史

## 🛠️ 故障排除

### 1. Python环境问题
**错误**: `python: command not found`
**解决**: 安装Python并添加到PATH环境变量

### 2. 依赖安装失败
**错误**: `pip install failed`
**解决**: 
- 升级pip: `python -m pip install --upgrade pip`
- 使用国内镜像: `pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/`

### 3. Playwright浏览器问题
**错误**: `Browser not found`
**解决**: 重新安装浏览器: `playwright install`

### 4. API Key配置问题
**错误**: `请先配置 DeepSeek API Key`
**解决**: 编辑 `.env` 文件，设置正确的API Key

### 5. 浏览器启动失败
**错误**: `Browser launch failed`
**解决**: 
- 检查是否有其他程序占用浏览器
- 尝试设置 `HEADLESS=true` 使用无头模式

## 📁 项目结构

```
AI_Agent/
├── browser-agent/          # 核心代码
│   ├── main.py            # 主程序入口
│   ├── agent.py           # AI智能体
│   ├── browser_controller.py # 浏览器控制器
│   └── config.py          # 配置管理
├── examples/              # 使用示例
├── docs/                  # 文档
├── .env                   # 环境配置
├── requirements.txt       # 依赖列表
├── install.ps1           # 安装脚本
└── start.ps1             # 启动脚本
```

## 🔗 相关链接

- [Browser Use GitHub](https://github.com/browser-use/browser-use)
- [DeepSeek API文档](https://platform.deepseek.com/api-docs/)
- [Playwright文档](https://playwright.dev/python/)

## 📞 获取帮助

如果遇到问题：
1. 查看本文档的故障排除部分
2. 检查日志文件 `browser_agent.log`
3. 运行示例程序测试功能
4. 确保所有前置条件都已满足

## 🎯 下一步计划

1. **扩展功能**: 添加更多浏览器操作类型
2. **Web界面**: 开发基于Web的用户界面
3. **知识库**: 集成本地知识库功能
4. **Dify集成**: 提供与Dify平台的集成接口
