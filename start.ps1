# Browser Use AI Agent 启动脚本

Write-Host "🚀 启动 Browser Use AI Agent..." -ForegroundColor Green

# 检查虚拟环境
if (-not (Test-Path "venv")) {
    Write-Host "❌ 虚拟环境不存在，请先运行 .\install.ps1" -ForegroundColor Red
    exit 1
}

# 检查配置文件
if (-not (Test-Path ".env")) {
    Write-Host "❌ 配置文件 .env 不存在" -ForegroundColor Red
    exit 1
}

# 检查API Key配置
$envContent = Get-Content ".env" -Raw
if ($envContent -match "DEEPSEEK_API_KEY=your_deepseek_api_key_here") {
    Write-Host "❌ 请先配置 DeepSeek API Key" -ForegroundColor Red
    Write-Host "编辑 .env 文件，设置 DEEPSEEK_API_KEY" -ForegroundColor Yellow
    Write-Host "获取API Key: https://platform.deepseek.com/" -ForegroundColor Cyan
    exit 1
}

# 激活虚拟环境
Write-Host "激活虚拟环境..." -ForegroundColor Yellow
& "venv\Scripts\Activate.ps1"

# 启动程序
Write-Host "启动AI智能体..." -ForegroundColor Yellow
python browser-agent/main.py
