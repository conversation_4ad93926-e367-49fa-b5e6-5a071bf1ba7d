# Browser Use AI Agent Start Script
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "Starting Browser Use AI Agent..." -ForegroundColor Green

# Check environment
if (-not (Test-Path "venv")) {
    Write-Host "Virtual environment not found. Please run .\install.ps1 first" -ForegroundColor Red
    exit 1
}

# Check API Key
$envContent = Get-Content ".env" -Raw -ErrorAction SilentlyContinue
if ($envContent -match "your_deepseek_api_key_here") {
    Write-Host "Please configure DeepSeek API Key in .env file first" -ForegroundColor Red
    exit 1
}

# Start program
& "venv\Scripts\Activate.ps1"
Set-Location browser-agent
python main.py
