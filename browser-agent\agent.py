"""
AI智能体 - 处理对话和任务规划
"""
import json
import logging
from typing import Dict, Any, List, Optional
from openai import OpenAI
from .config import get_deepseek_config
from .browser_controller import BrowserController

logger = logging.getLogger(__name__)


class AIAgent:
    """AI智能体"""
    
    def __init__(self):
        self.deepseek_config = get_deepseek_config()
        self.client = OpenAI(
            api_key=self.deepseek_config["api_key"],
            base_url=self.deepseek_config["base_url"]
        )
        self.browser_controller = BrowserController()
        self.conversation_history: List[Dict[str, str]] = []
    
    async def initialize(self):
        """初始化智能体"""
        await self.browser_controller.initialize()
        logger.info("AI智能体初始化成功")
    
    def _create_system_prompt(self) -> str:
        """创建系统提示词"""
        return """你是一个专业的浏览器自动化AI助手，能够理解用户的需求并执行相应的浏览器操作。

你的能力包括：
1. 网页导航和浏览
2. 表单填充和提交
3. 数据提取和收集
4. 页面元素交互（点击、输入、选择等）
5. 截图和页面分析

当用户提出请求时，你需要：
1. 理解用户的意图
2. 分析需要执行的操作步骤
3. 调用相应的浏览器控制功能
4. 返回执行结果和反馈

请用中文回复用户，并保持友好和专业的语调。"""
    
    def _parse_user_intent(self, user_input: str) -> Dict[str, Any]:
        """解析用户意图"""
        # 简单的意图识别，可以后续扩展为更复杂的NLP处理
        intent_keywords = {
            "navigate": ["打开", "访问", "导航", "跳转", "进入"],
            "fill_form": ["填写", "填充", "输入", "表单"],
            "extract_data": ["提取", "获取", "收集", "抓取", "数据"],
            "click": ["点击", "按", "选择"],
            "screenshot": ["截图", "截屏", "保存图片"],
            "search": ["搜索", "查找", "寻找"]
        }
        
        user_lower = user_input.lower()
        detected_intents = []
        
        for intent, keywords in intent_keywords.items():
            if any(keyword in user_lower for keyword in keywords):
                detected_intents.append(intent)
        
        return {
            "intents": detected_intents,
            "original_text": user_input
        }
    
    async def chat(self, user_input: str) -> Dict[str, Any]:
        """处理用户对话"""
        try:
            # 添加到对话历史
            self.conversation_history.append({"role": "user", "content": user_input})
            
            # 解析用户意图
            intent_analysis = self._parse_user_intent(user_input)
            
            # 构建对话消息
            messages = [
                {"role": "system", "content": self._create_system_prompt()},
                *self.conversation_history[-10:]  # 保留最近10轮对话
            ]
            
            # 调用DeepSeek API
            response = self.client.chat.completions.create(
                model=self.deepseek_config["model"],
                messages=messages,
                temperature=0.7,
                max_tokens=1000
            )
            
            ai_response = response.choices[0].message.content
            
            # 添加AI回复到历史
            self.conversation_history.append({"role": "assistant", "content": ai_response})
            
            # 分析是否需要执行浏览器操作
            browser_action = await self._analyze_browser_action(user_input, ai_response)
            
            return {
                "success": True,
                "response": ai_response,
                "intent_analysis": intent_analysis,
                "browser_action": browser_action,
                "conversation_id": len(self.conversation_history)
            }
            
        except Exception as e:
            logger.error(f"对话处理失败: {e}")
            return {
                "success": False,
                "response": f"抱歉，处理您的请求时出现错误: {str(e)}",
                "error": str(e)
            }
    
    async def _analyze_browser_action(self, user_input: str, ai_response: str) -> Optional[Dict[str, Any]]:
        """分析是否需要执行浏览器操作"""
        # 检查用户输入中是否包含具体的操作指令
        action_patterns = {
            "navigate": ["打开", "访问", "http", "www", ".com", ".cn"],
            "fill_form": ["填写表单", "填充", "登录", "注册"],
            "extract": ["提取数据", "获取信息", "收集"],
            "screenshot": ["截图", "截屏"]
        }
        
        user_lower = user_input.lower()
        
        # 导航操作
        if any(pattern in user_lower for pattern in action_patterns["navigate"]):
            # 尝试提取URL
            import re
            url_pattern = r'https?://[^\s]+'
            urls = re.findall(url_pattern, user_input)
            if urls:
                return {
                    "type": "navigate",
                    "url": urls[0],
                    "description": "导航到指定网页"
                }
        
        # 截图操作
        if any(pattern in user_lower for pattern in action_patterns["screenshot"]):
            return {
                "type": "screenshot",
                "description": "截取当前页面"
            }
        
        return None
    
    async def execute_browser_task(self, task_type: str, **kwargs) -> Dict[str, Any]:
        """执行浏览器任务"""
        try:
            if task_type == "navigate":
                url = kwargs.get("url")
                if url:
                    result = await self.browser_controller.execute_task(
                        f"请访问 {url}", url
                    )
                    return result
            
            elif task_type == "fill_form":
                form_data = kwargs.get("form_data", {})
                submit = kwargs.get("submit", False)
                return await self.browser_controller.fill_form(form_data, submit)
            
            elif task_type == "extract_data":
                rules = kwargs.get("extraction_rules", [])
                return await self.browser_controller.extract_data(rules)
            
            elif task_type == "screenshot":
                screenshot_path = await self.browser_controller.take_screenshot()
                return {
                    "success": True,
                    "result": screenshot_path,
                    "message": "截图已保存"
                }
            
            elif task_type == "custom":
                task = kwargs.get("task", "")
                url = kwargs.get("url")
                return await self.browser_controller.execute_task(task, url)
            
            else:
                return {
                    "success": False,
                    "message": f"不支持的任务类型: {task_type}"
                }
                
        except Exception as e:
            logger.error(f"浏览器任务执行失败: {e}")
            return {
                "success": False,
                "message": f"任务执行失败: {str(e)}"
            }
    
    async def get_page_status(self) -> Dict[str, Any]:
        """获取当前页面状态"""
        return await self.browser_controller.get_page_info()
    
    async def close(self):
        """关闭智能体"""
        await self.browser_controller.close()
        logger.info("AI智能体已关闭")
