# AI智能体项目

基于Dify + Browser Use的AI智能体，支持浏览器自动化操作和知识库问答。

## 功能特性

- 🤖 基于DeepSeek大模型的智能对话
- 📚 本地知识库RAG检索
- 🌐 浏览器自动化操作
- 📝 智能表单填充和数据提取
- 🔄 可视化工作流编排

## 技术栈

- **AI平台**: Dify
- **大模型**: DeepSeek
- **浏览器自动化**: Browser Use
- **部署**: Docker + Docker Compose

## 项目结构

```
AI_Agent/
├── dify/                   # Dify平台部署文件
├── browser-use/            # Browser Use服务
├── knowledge-base/         # 知识库文件
├── workflows/              # Dify工作流配置
├── docs/                   # 项目文档
└── README.md
```

## 快速开始

详细部署说明请参考 [部署文档](docs/deployment.md)

## 实现进度

### ✅ 已完成
- [x] 项目初始化
- [x] 项目目录结构创建
- [x] Dify Docker配置文件
- [x] 部署脚本和文档
- [x] Docker配置文件修复

### 🔄 进行中
- [x] Dify平台部署准备
- [ ] 等待Docker Desktop启动

### ⚠️ 需要用户操作
- [ ] 启动Docker Desktop应用程序
- [ ] 配置DeepSeek API Key

### ⏳ 待完成
- [ ] 启动Dify服务
- [ ] DeepSeek模型配置
- [ ] Browser Use集成
- [ ] 知识库配置
- [ ] 工作流开发
