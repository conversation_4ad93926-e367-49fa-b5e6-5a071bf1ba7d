# AI智能体项目

基于Browser Use的轻量级AI智能体，专注于浏览器自动化操作和智能对话。

## 功能特性

- 🤖 基于DeepSeek大模型的智能对话
- 🌐 浏览器自动化操作
- 📝 智能表单填充和数据提取
- 🎯 轻量级、快速部署
- 🔌 可扩展到现有Dify平台

## 技术栈

- **大模型**: DeepSeek API
- **浏览器自动化**: Browser Use
- **后端**: FastAPI
- **前端**: Web界面 + 命令行
- **部署**: Python环境

## 项目结构

```
AI_Agent/
├── browser-agent/          # Browser Use智能体核心
│   ├── main.py            # 主程序入口
│   ├── agent.py           # AI智能体逻辑
│   ├── browser_controller.py # 浏览器控制器
│   └── config.py          # 配置文件
├── web-ui/                # Web界面
├── examples/              # 使用示例
├── docs/                  # 项目文档
└── README.md
```

## 快速开始

详细部署说明请参考 [部署文档](docs/browser-use-setup.md)

## 实现进度

### ✅ 已完成
- [x] 项目初始化
- [x] 技术方案调整（改为Browser Use方案）
- [x] 项目结构创建
- [x] 核心代码开发
- [x] 配置文件和环境设置
- [x] 安装和启动脚本
- [x] 使用示例和文档

### 🔄 进行中
- [ ] 依赖包安装测试

### ⏳ 待完成
- [ ] DeepSeek API Key配置
- [ ] 功能测试和验证
- [ ] Web界面开发（可选）
- [ ] 知识库集成（可选）
- [ ] Dify平台集成（可选）
