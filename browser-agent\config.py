"""
配置文件 - Browser Use AI Agent
"""
import os
from typing import Optional
from pydantic import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # DeepSeek API配置
    DEEPSEEK_API_KEY: str = ""
    DEEPSEEK_API_BASE: str = "https://api.deepseek.com"
    DEEPSEEK_MODEL: str = "deepseek-chat"
    
    # 浏览器配置
    BROWSER_TYPE: str = "chromium"  # chromium, firefox, webkit
    HEADLESS: bool = False  # 是否无头模式
    BROWSER_TIMEOUT: int = 30000  # 浏览器超时时间(毫秒)
    
    # 服务配置
    HOST: str = "localhost"
    PORT: int = 8000
    DEBUG: bool = True
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "browser_agent.log"
    
    # 任务配置
    MAX_STEPS: int = 50  # 最大执行步数
    STEP_DELAY: float = 1.0  # 步骤间延迟(秒)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 全局配置实例
settings = Settings()


def get_deepseek_config() -> dict:
    """获取DeepSeek配置"""
    return {
        "api_key": settings.DEEPSEEK_API_KEY,
        "base_url": settings.DEEPSEEK_API_BASE,
        "model": settings.DEEPSEEK_MODEL
    }


def get_browser_config() -> dict:
    """获取浏览器配置"""
    return {
        "browser_type": settings.BROWSER_TYPE,
        "headless": settings.HEADLESS,
        "timeout": settings.BROWSER_TIMEOUT
    }
