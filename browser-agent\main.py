"""
Browser Use AI Agent - 主程序入口
"""
import asyncio
import logging
import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from agent import AIAgent
from config import settings

# 🔧 关键修复：在导入任何Browser Use相关模块之前设置环境变量
def setup_browser_environment():
    """在程序启动时立即设置浏览器环境变量"""
    # OpenAI API兼容设置
    os.environ["OPENAI_API_KEY"] = settings.DEEPSEEK_API_KEY
    os.environ["OPENAI_API_BASE"] = settings.DEEPSEEK_API_BASE
    os.environ["OPENAI_BASE_URL"] = settings.DEEPSEEK_API_BASE

    # 浏览器可见性设置 - 多种方式确保生效
    os.environ["BROWSER_USE_HEADLESS"] = "false"
    os.environ["BROWSER_USE_BROWSER_TYPE"] = "chromium"
    os.environ["PLAYWRIGHT_HEADLESS"] = "false"
    os.environ["HEADLESS"] = "false"
    os.environ["BROWSER_HEADLESS"] = "false"

    # 🔧 移除调试模式，避免调试页面弹出
    # os.environ["PWDEBUG"] = "1"  # 注释掉调试模式

    print("🔧 浏览器环境变量已设置 - 浏览器将以可见模式运行")

# 立即设置环境变量
setup_browser_environment()

# 配置日志
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(settings.LOG_FILE, encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class BrowserAgentCLI:
    """命令行界面"""

    def __init__(self):
        self.agent = AIAgent()
        self.running = False

    async def start(self):
        """启动智能体"""
        try:
            await self.agent.initialize()
            self.running = True
            logger.info("Browser Use AI Agent 启动成功!")

            print("🤖 Browser Use AI Agent")
            print("=" * 50)
            print("欢迎使用浏览器自动化AI助手!")
            print("您可以通过自然语言与我对话，我会帮您执行浏览器操作。")
            print("输入 'help' 查看帮助，输入 'quit' 退出程序。")
            print("=" * 50)

            await self.run_interactive_mode()

        except Exception as e:
            logger.error(f"启动失败: {e}")
            print(f"❌ 启动失败: {e}")

    async def run_interactive_mode(self):
        """运行交互模式"""
        while self.running:
            try:
                # 获取用户输入
                user_input = input("\n👤 您: ").strip()

                if not user_input:
                    continue

                # 处理特殊命令
                if user_input.lower() in ['quit', 'exit', '退出']:
                    await self.stop()
                    break
                elif user_input.lower() == 'help':
                    self.show_help()
                    continue
                elif user_input.lower() == 'status':
                    await self.show_status()
                    continue
                elif user_input.lower() == 'screenshot':
                    await self.take_screenshot()
                    continue

                # 处理对话
                print("🤖 AI: 正在处理您的请求...")
                response = await self.agent.chat(user_input)

                if response["success"]:
                    print(f"🤖 AI: {response['response']}")

                    # 如果有浏览器操作，执行它
                    if response.get("browser_action"):
                        action = response["browser_action"]
                        print(f"🔧 执行操作: {action['description']}")

                        if action["type"] == "navigate":
                            result = await self.agent.execute_browser_task(
                                "navigate", url=action["url"]
                            )
                        elif action["type"] == "screenshot":
                            result = await self.agent.execute_browser_task("screenshot")
                        else:
                            result = {"success": False, "message": "未实现的操作类型"}

                        if result["success"]:
                            print(f"✅ 操作成功: {result.get('message', '完成')}")
                        else:
                            print(f"❌ 操作失败: {result.get('message', '未知错误')}")
                else:
                    print(f"❌ 错误: {response.get('response', '处理失败')}")

            except KeyboardInterrupt:
                print("\n\n👋 检测到中断信号，正在退出...")
                await self.stop()
                break
            except Exception as e:
                logger.error(f"交互模式错误: {e}")
                print(f"❌ 发生错误: {e}")

    def show_help(self):
        """显示帮助信息"""
        help_text = """
🔧 Browser Use AI Agent 帮助

基本命令:
  help     - 显示此帮助信息
  status   - 显示当前页面状态
  screenshot - 截取当前页面
  quit     - 退出程序

使用示例:
  "打开 https://www.baidu.com"
  "在搜索框中输入 Python"
  "点击搜索按钮"
  "提取页面中的所有链接"
  "填写表单：用户名是张三，密码是123456"
  "截图保存当前页面"

功能特性:
  ✅ 网页导航和浏览
  ✅ 智能表单填充
  ✅ 数据提取和收集
  ✅ 页面元素交互
  ✅ 截图和页面分析
  ✅ 自然语言对话
        """
        print(help_text)

    async def show_status(self):
        """显示当前状态"""
        try:
            page_info = await self.agent.get_page_status()
            if page_info:
                print(f"📄 当前页面: {page_info.get('title', '未知')}")
                print(f"🔗 URL: {page_info.get('url', '未知')}")
            else:
                print("📄 当前没有打开的页面")
        except Exception as e:
            print(f"❌ 获取状态失败: {e}")

    async def take_screenshot(self):
        """截图"""
        try:
            result = await self.agent.execute_browser_task("screenshot")
            if result["success"]:
                print(f"📸 截图已保存: {result['result']}")
            else:
                print(f"❌ 截图失败: {result['message']}")
        except Exception as e:
            print(f"❌ 截图失败: {e}")

    async def stop(self):
        """停止智能体"""
        self.running = False
        await self.agent.close()
        print("👋 Browser Use AI Agent 已退出，感谢使用!")


async def main():
    """主函数"""
    # 检查配置
    if not settings.DEEPSEEK_API_KEY or settings.DEEPSEEK_API_KEY == "your_deepseek_api_key_here":
        print("❌ 请先配置 DeepSeek API Key")
        print("编辑 .env 文件，设置 DEEPSEEK_API_KEY")
        return

    # 启动CLI
    cli = BrowserAgentCLI()
    await cli.start()


if __name__ == "__main__":
    asyncio.run(main())
