# 部署指南

## 前置要求

1. **Docker Desktop** - 确保已安装并运行
   - 下载地址: https://www.docker.com/products/docker-desktop/
   - ⚠️ **重要**: 启动Docker Desktop后，确保右下角Docker图标显示绿色（运行中）
   - 分配至少4GB内存给Docker
2. **DeepSeek API Key** - 从 [DeepSeek 官网](https://platform.deepseek.com/) 获取

## 快速部署

### 1. 配置 DeepSeek API

编辑 `dify/.env` 文件，填入您的 DeepSeek API Key：

```bash
DEEPSEEK_API_KEY=your_actual_api_key_here
```

### 2. 启动 Dify 平台

在项目根目录执行：

```powershell
cd dify
.\start.ps1
```

### 3. 访问平台

- **管理控制台**: http://localhost
- **默认密码**: admin123456

## 服务说明

| 服务 | 端口 | 说明 |
|------|------|------|
| nginx | 80 | 反向代理和Web入口 |
| api | 5001 | Dify API服务 |
| web | 3000 | Dify Web界面 |
| db | 5432 | PostgreSQL数据库 |
| redis | 6379 | Redis缓存 |
| qdrant | 6333 | 向量数据库 |

## 常用命令

```powershell
# 启动服务
cd dify
.\start.ps1

# 停止服务
.\stop.ps1

# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 查看服务状态
docker-compose ps
```

## 故障排除

### 1. Docker Desktop未运行
**错误信息**: `open //./pipe/dockerDesktopLinuxEngine: The system cannot find the file specified`

**解决方案**:
1. 启动Docker Desktop应用程序
2. 等待Docker完全启动（右下角图标变绿）
3. 重新运行部署命令

### 2. 端口冲突
如果80端口被占用，修改 `docker-compose.yml` 中的端口映射：
```yaml
ports:
  - "8080:80"  # 改为8080端口
```

### 3. 内存不足
确保Docker分配至少4GB内存。

### 4. 服务启动失败
查看具体错误日志：
```powershell
docker-compose logs [service_name]
```

### 5. 配置文件错误
如果遇到YAML格式错误，检查配置文件缩进和语法。

## 下一步

部署完成后，请参考：
- [配置DeepSeek模型](model-config.md)
- [创建知识库](knowledge-base.md)
- [Browser Use集成](browser-use.md)
