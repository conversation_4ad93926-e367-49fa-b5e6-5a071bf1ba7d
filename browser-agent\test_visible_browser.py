"""
测试浏览器可见性的简单脚本
"""
import asyncio
import os
from browser_use import Agent
from langchain_openai import ChatOpenAI

async def test_visible_browser():
    """测试可见浏览器"""
    
    # 设置环境变量
    os.environ["OPENAI_API_KEY"] = "***********************************"
    os.environ["OPENAI_API_BASE"] = "https://api.deepseek.com/v1"
    
    # 强制设置浏览器为可见模式
    os.environ["BROWSER_USE_HEADLESS"] = "false"
    os.environ["PLAYWRIGHT_HEADLESS"] = "false"
    os.environ["HEADLESS"] = "false"
    
    print("🚀 启动可见浏览器测试...")
    
    # 创建LLM客户端
    llm = ChatOpenAI(
        model="deepseek-chat",
        api_key="***********************************",
        base_url="https://api.deepseek.com/v1",
        temperature=0.7
    )
    
    # 创建Agent
    agent = Agent(
        task="打开百度网站并搜索人工智能",
        llm=llm
    )
    
    print("🌐 正在打开浏览器...")
    print("📌 请注意查看是否有浏览器窗口弹出")
    
    try:
        # 执行任务
        result = await agent.run(max_steps=10)
        print(f"✅ 任务完成: {result}")
        
        # 等待用户观察
        input("按回车键继续...")
        
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    asyncio.run(test_visible_browser())
