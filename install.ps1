# Browser Use AI Agent Install Script
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "Installing Browser Use AI Agent..." -ForegroundColor Green

# Check Python
try {
    $pythonVersion = python --version 2>&1
    Write-Host "Python found: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "Python not found. Please install Python 3.8+" -ForegroundColor Red
    exit 1
}

# Create virtual environment
if (-not (Test-Path "venv")) {
    Write-Host "Creating virtual environment..." -ForegroundColor Yellow
    python -m venv venv
}

# Install dependencies
Write-Host "Installing dependencies..." -ForegroundColor Yellow
& "venv\Scripts\Activate.ps1"
python -m pip install --upgrade pip
pip install -r requirements.txt
playwright install

Write-Host ""
Write-Host "Installation completed!" -ForegroundColor Green
Write-Host "1. Edit .env file to configure DeepSeek API Key" -ForegroundColor Yellow
Write-Host "2. Run .\start.ps1 to start the program" -ForegroundColor Yellow
