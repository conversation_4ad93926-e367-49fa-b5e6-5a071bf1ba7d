# Browser Use AI Agent 安装脚本

Write-Host "🚀 Browser Use AI Agent 安装程序" -ForegroundColor Green
Write-Host "=" * 50

# 检查Python版本
Write-Host "检查Python环境..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ Python版本: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python未安装或不在PATH中" -ForegroundColor Red
    Write-Host "请先安装Python 3.8+: https://www.python.org/downloads/" -ForegroundColor Yellow
    exit 1
}

# 创建虚拟环境
Write-Host "创建Python虚拟环境..." -ForegroundColor Yellow
if (Test-Path "venv") {
    Write-Host "虚拟环境已存在，跳过创建" -ForegroundColor Cyan
} else {
    python -m venv venv
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 虚拟环境创建成功" -ForegroundColor Green
    } else {
        Write-Host "❌ 虚拟环境创建失败" -ForegroundColor Red
        exit 1
    }
}

# 激活虚拟环境
Write-Host "激活虚拟环境..." -ForegroundColor Yellow
& "venv\Scripts\Activate.ps1"

# 升级pip
Write-Host "升级pip..." -ForegroundColor Yellow
python -m pip install --upgrade pip

# 安装依赖
Write-Host "安装项目依赖..." -ForegroundColor Yellow
pip install -r requirements.txt

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ 依赖安装成功" -ForegroundColor Green
} else {
    Write-Host "❌ 依赖安装失败" -ForegroundColor Red
    exit 1
}

# 安装Playwright浏览器
Write-Host "安装Playwright浏览器..." -ForegroundColor Yellow
playwright install

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Playwright浏览器安装成功" -ForegroundColor Green
} else {
    Write-Host "❌ Playwright浏览器安装失败" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🎉 安装完成!" -ForegroundColor Green
Write-Host ""
Write-Host "下一步操作:" -ForegroundColor Cyan
Write-Host "1. 编辑 .env 文件，配置您的 DeepSeek API Key" -ForegroundColor Yellow
Write-Host "2. 运行 .\start.ps1 启动程序" -ForegroundColor Yellow
Write-Host ""
Write-Host "获取 DeepSeek API Key: https://platform.deepseek.com/" -ForegroundColor Cyan
