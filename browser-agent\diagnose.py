"""
Browser Use AI Agent 诊断脚本
用于排查浏览器可见性和API兼容性问题
"""
import asyncio
import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

def diagnose_environment():
    """诊断环境变量"""
    print("🔍 诊断环境变量...")

    # 硬编码配置用于测试
    api_key = "sk-894f9c06a74949e0bf2e99208a983497"
    base_url = "https://api.deepseek.com/v1"

    env_vars = {
        "OPENAI_API_KEY": api_key,
        "OPENAI_API_BASE": base_url,
        "OPENAI_BASE_URL": base_url,
        "BROWSER_USE_HEADLESS": "false",
        "PLAYWRIGHT_HEADLESS": "false",
        "HEADLESS": "false",
        "BROWSER_HEADLESS": "false",
        "PWDEBUG": "1"
    }

    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"✅ 设置 {key} = {value[:20]}...")

    print("✅ 环境变量设置完成")
    return api_key, base_url

async def diagnose_api(api_key, base_url):
    """诊断API连接"""
    print("\n🔍 诊断API连接...")

    try:
        from langchain_openai import ChatOpenAI

        print(f"✅ 配置信息")
        print(f"   - API Key: {api_key[:10]}...")
        print(f"   - Base URL: {base_url}")
        print(f"   - Model: deepseek-chat")

        # 测试简单的API调用
        llm = ChatOpenAI(
            model="deepseek-chat",
            api_key=api_key,
            base_url=base_url,
            temperature=0.7,
            max_tokens=100
        )

        response = await llm.ainvoke("Hello, please respond with 'API working'")
        print(f"✅ API测试成功: {response.content}")
        return True, llm

    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False, None

async def diagnose_browser(llm):
    """诊断浏览器启动"""
    print("\n🔍 诊断浏览器启动...")

    try:
        from browser_use import Agent

        print("✅ LLM客户端已准备")

        # 创建Agent
        agent = Agent(
            task="简单测试：打开百度网站",
            llm=llm
        )

        print("✅ Agent创建成功")
        print("🌐 正在启动浏览器...")
        print("📌 请注意观察是否有浏览器窗口出现")

        # 执行简单任务
        result = await agent.run(max_steps=3)

        print(f"✅ 浏览器任务完成")
        print(f"   结果类型: {type(result)}")

        return True

    except Exception as e:
        print(f"❌ 浏览器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主诊断流程"""
    print("🚀 Browser Use AI Agent 诊断开始")
    print("=" * 50)

    # 步骤1：诊断环境变量
    api_key, base_url = diagnose_environment()

    # 步骤2：诊断API连接
    api_ok, llm = await diagnose_api(api_key, base_url)

    if not api_ok:
        print("\n❌ API连接失败，请检查配置")
        return

    # 步骤3：诊断浏览器
    browser_ok = await diagnose_browser(llm)

    print("\n" + "=" * 50)
    if api_ok and browser_ok:
        print("🎉 诊断完成 - 所有组件正常工作")
    else:
        print("⚠️ 诊断发现问题，请查看上述错误信息")

    print("\n📝 诊断报告:")
    print(f"   - API连接: {'✅ 正常' if api_ok else '❌ 异常'}")
    print(f"   - 浏览器: {'✅ 正常' if browser_ok else '❌ 异常'}")

    if browser_ok:
        print("\n💡 提示:")
        print("   - 如果看到浏览器窗口，说明可见性设置正确")
        print("   - 如果没看到窗口，请检查任务栏或Alt+Tab")
        print("   - 查看生成的agent_history.gif文件了解操作过程")

    # 等待用户确认
    input("\n按回车键退出...")

if __name__ == "__main__":
    asyncio.run(main())
