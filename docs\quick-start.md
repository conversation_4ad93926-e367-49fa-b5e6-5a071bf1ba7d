# 快速启动指南

## 📋 当前状态

✅ **已完成的工作**:
- 项目结构已创建
- Dify Docker配置已准备就绪
- 部署脚本已创建
- 文档已编写

## 🚀 下一步操作

### 1. 启动Docker Desktop
**重要**: 请确保Docker Desktop正在运行
- 在Windows开始菜单中找到"Docker Desktop"
- 启动应用程序
- 等待右下角Docker图标变为绿色
- 确保Docker分配至少4GB内存

### 2. 配置DeepSeek API
编辑文件 `dify/.env`，将以下行：
```
DEEPSEEK_API_KEY=your_deepseek_api_key_here
```
替换为您的实际API Key：
```
DEEPSEEK_API_KEY=sk-your-actual-key-here
```

### 3. 启动Dify平台
在PowerShell中执行：
```powershell
cd dify
.\start.ps1
```

或者手动执行：
```powershell
cd dify
docker-compose up -d
```

### 4. 访问平台
- 打开浏览器访问: http://localhost
- 默认管理员密码: `admin123456`

## 🔧 如果遇到问题

### Docker相关错误
```
open //./pipe/dockerDesktopLinuxEngine: The system cannot find the file specified
```
**解决**: 启动Docker Desktop应用程序

### 端口占用
```
bind: address already in use
```
**解决**: 修改 `docker-compose.yml` 中的端口映射为其他端口

### 内存不足
**解决**: 在Docker Desktop设置中增加内存分配

## 📞 需要帮助？

如果遇到任何问题，请：
1. 检查 [部署文档](deployment.md) 中的故障排除部分
2. 查看Docker日志: `docker-compose logs`
3. 确保所有前置条件都已满足

## 🎯 完成后的下一步

Dify启动成功后，我们将继续：
1. 配置DeepSeek模型
2. 集成Browser Use
3. 创建知识库
4. 开发智能体工作流
